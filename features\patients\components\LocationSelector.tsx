'use client';

import { useState, useEffect } from 'react';
import { Country, State, City } from 'country-state-city';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';

interface LocationSelectorProps {
  countryValue: string;
  stateValue: string;
  cityValue: string;
  onCountryChange: (value: string) => void;
  onStateChange: (value: string) => void;
  onCityChange: (value: string) => void;
  countryError?: string;
  stateError?: string;
  cityError?: string;
}

export function LocationSelector({
  countryValue,
  stateValue,
  cityValue,
  onCountryChange,
  onStateChange,
  onCityChange,
  countryError,
  stateError,
  cityError
}: LocationSelectorProps) {
  const [countries, setCountries] = useState<any[]>([]);
  const [states, setStates] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);
  const [selectedCountryCode, setSelectedCountryCode] = useState<string>('');
  const [selectedStateCode, setSelectedStateCode] = useState<string>('');
  const [showCityInput, setShowCityInput] = useState<boolean>(false);

  // Load countries on component mount
  useEffect(() => {
    const allCountries = Country.getAllCountries();
    setCountries(allCountries);
    
    // Set Argentina as default if no country is selected
    if (!countryValue && allCountries.length > 0) {
      const argentina = allCountries.find(country => country.name === 'Argentina');
      if (argentina) {
        setSelectedCountryCode(argentina.isoCode);
        onCountryChange('Argentina');
      }
    } else if (countryValue) {
      // Find country code for existing value
      const existingCountry = allCountries.find(country => country.name === countryValue);
      if (existingCountry) {
        setSelectedCountryCode(existingCountry.isoCode);
      }
    }
  }, []);

  // Load states when country changes
  useEffect(() => {
    if (selectedCountryCode) {
      const countryStates = State.getStatesOfCountry(selectedCountryCode);
      setStates(countryStates);
      
      // Reset state and city when country changes
      if (countryStates.length === 0) {
        setSelectedStateCode('');
        onStateChange('');
        setCities([]);
        onCityChange('');
      }
    } else {
      setStates([]);
      setCities([]);
    }
  }, [selectedCountryCode]);

  // Load cities when state changes
  useEffect(() => {
    if (selectedCountryCode && selectedStateCode) {
      const stateCities = City.getCitiesOfState(selectedCountryCode, selectedStateCode);
      setCities(stateCities);
      
      // Check if the current city exists in the list
      const cityExists = stateCities.some(city => city.name === cityValue);
      
      // Show input if no cities available or if current city doesn't exist in the list
      setShowCityInput(stateCities.length === 0 || (cityValue && !cityExists));
    } else {
      setCities([]);
      setShowCityInput(true); // Show input if no state selected
    }
  }, [selectedCountryCode, selectedStateCode, cityValue]);

  const handleCountryChange = (value: string) => {
    const selectedCountry = countries.find(country => country.name === value);
    if (selectedCountry) {
      setSelectedCountryCode(selectedCountry.isoCode);
      onCountryChange(value);
      
      // Reset state and city
      setSelectedStateCode('');
      onStateChange('');
      onCityChange('');
    }
  };

  const handleStateChange = (value: string) => {
    const selectedState = states.find(state => state.name === value);
    if (selectedState) {
      setSelectedStateCode(selectedState.isoCode);
      onStateChange(value);
      
      // Reset city
      onCityChange('');
    }
  };

  const handleCityChange = (value: string) => {
    onCityChange(value);
  };

  return (
    <>
      {/* Country Selector */}
      <FormItem>
        <FormLabel className="text-[#808080]">País</FormLabel>
        <FormControl>
          <Select value={countryValue} onValueChange={handleCountryChange}>
            <SelectTrigger className="border-[#DCDBDB]">
              <SelectValue placeholder="Seleccionar País" />
            </SelectTrigger>
            <SelectContent>
              {countries.map((country) => (
                <SelectItem key={country.isoCode} value={country.name}>
                  {country.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FormControl>
        {countryError && <FormMessage>{countryError}</FormMessage>}
      </FormItem>

      {/* State/Province Selector */}
      <FormItem>
        <FormLabel className="text-[#808080]">Provincia/Estado</FormLabel>
        <FormControl>
          <Select 
            value={stateValue} 
            onValueChange={handleStateChange}
            disabled={!selectedCountryCode || states.length === 0}
          >
            <SelectTrigger className="border-[#DCDBDB]">
              <SelectValue placeholder="Seleccionar Provincia/Estado" />
            </SelectTrigger>
            <SelectContent>
              {states.map((state) => (
                <SelectItem key={state.isoCode} value={state.name}>
                  {state.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FormControl>
        {stateError && <FormMessage>{stateError}</FormMessage>}
      </FormItem>

      {/* City Selector or Input */}
      <FormItem>
        <FormLabel className="text-[#808080]">Ciudad</FormLabel>
        <FormControl>
          {showCityInput || cities.length === 0 ? (
            <Input
              placeholder="Ingrese la ciudad"
              className="border-[#DCDBDB]"
              value={cityValue}
              onChange={(e) => handleCityChange(e.target.value)}
            />
          ) : (
            <Select value={cityValue} onValueChange={handleCityChange}>
              <SelectTrigger className="border-[#DCDBDB]">
                <SelectValue placeholder="Seleccionar Ciudad" />
              </SelectTrigger>
              <SelectContent>
                {cities.map((city) => (
                  <SelectItem key={city.name} value={city.name}>
                    {city.name}
                  </SelectItem>
                ))}
                <SelectItem value="__custom__">
                  <span className="text-blue-600">Escribir ciudad manualmente</span>
                </SelectItem>
              </SelectContent>
            </Select>
          )}
        </FormControl>
        {cityError && <FormMessage>{cityError}</FormMessage>}
        {!showCityInput && cities.length > 0 && (
          <p className="text-xs text-gray-500 mt-1">
            ¿No encuentras tu ciudad? Selecciona "Escribir ciudad manualmente"
          </p>
        )}
      </FormItem>
    </>
  );
}
