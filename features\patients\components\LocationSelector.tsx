'use client';

import { useState, useEffect, useMemo } from 'react';
import {
  CountrySelect,
  StateSelect,
  CitySelect
} from 'react-country-state-city';
import 'react-country-state-city/dist/react-country-state-city.css';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';

// Use fallback cities database for better compatibility

// Fallback cities database for major cities
const fallbackCities = {
  'Argentina': {
    'Buenos Aires': [
      'Buenos Aires', 'La Plata', 'Mar del Plata', 'Bahía Blanca', 'Tandil',
      'Olavarría', 'Pergamino', 'Azul', 'Necochea', 'San Nicolás de los Arroyos',
      'Campana', 'Zárate', '<PERSON>j<PERSON>', '<PERSON>', 'Chivilcoy', '<PERSON><PERSON>', '<PERSON>',
      '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
      '<PERSON>', '<PERSON>', '<PERSON>ig<PERSON>', '<PERSON> Fernando', '<PERSON>rón', 'Quilmes',
      'Avellaneda', 'Lanús', 'Lomas de Zamora', 'Almirante Brown', 'Esteban Echeverría',
      'Florencio Varela', 'Berazategui', 'La Matanza', 'Merlo', 'Moreno', 'José C. Paz',
      'Malvinas Argentinas', 'San Miguel', 'Hurlingham', 'Ituzaingó', 'Tres de Febrero'
    ],
    'Córdoba': [
      'Córdoba', 'Villa Carlos Paz', 'Río Cuarto', 'San Francisco', 'Villa María',
      'Alta Gracia', 'Jesús María', 'La Falda', 'Bell Ville', 'Marcos Juárez'
    ],
    'Santa Fe': [
      'Santa Fe', 'Rosario', 'Rafaela', 'Venado Tuerto', 'Reconquista', 'Esperanza'
    ],
    'Mendoza': [
      'Mendoza', 'San Rafael', 'Godoy Cruz', 'Maipú', 'Las Heras', 'Luján de Cuyo'
    ],
    'Tucumán': [
      'San Miguel de Tucumán', 'Yerba Buena', 'Tafí Viejo', 'Banda del Río Salí'
    ],
    'Entre Ríos': [
      'Paraná', 'Concordia', 'Gualeguaychú', 'Concepción del Uruguay'
    ],
    'Salta': [
      'Salta', 'San Ramón de la Nueva Orán', 'Tartagal', 'Metán'
    ],
    'Misiones': [
      'Posadas', 'Puerto Iguazú', 'Oberá', 'Eldorado'
    ],
    'Chaco': [
      'Resistencia', 'Barranqueras', 'Puerto Vilelas', 'Fontana'
    ],
    'Corrientes': [
      'Corrientes', 'Goya', 'Mercedes', 'Paso de los Libres'
    ],
    'Santiago del Estero': [
      'Santiago del Estero', 'La Banda', 'Termas de Río Hondo'
    ],
    'Jujuy': [
      'San Salvador de Jujuy', 'Palpalá', 'Perico', 'San Pedro'
    ],
    'Río Negro': [
      'Viedma', 'San Carlos de Bariloche', 'General Roca', 'Cipolletti'
    ],
    'Neuquén': [
      'Neuquén', 'Plottier', 'Centenario', 'Cutral Có'
    ],
    'Formosa': [
      'Formosa', 'Clorinda', 'Pirané', 'El Colorado'
    ],
    'Chubut': [
      'Rawson', 'Comodoro Rivadavia', 'Puerto Madryn', 'Trelew'
    ],
    'San Luis': [
      'San Luis', 'Villa Mercedes', 'Merlo', 'La Punta'
    ],
    'Catamarca': [
      'San Fernando del Valle de Catamarca', 'Andalgalá', 'Belén'
    ],
    'La Rioja': [
      'La Rioja', 'Chilecito', 'Aimogasta'
    ],
    'San Juan': [
      'San Juan', 'Chimbas', 'Rivadavia', 'Santa Lucía'
    ],
    'La Pampa': [
      'Santa Rosa', 'General Pico', 'Toay'
    ],
    'Santa Cruz': [
      'Río Gallegos', 'Caleta Olivia', 'Pico Truncado'
    ],
    'Tierra del Fuego': [
      'Ushuaia', 'Río Grande'
    ]
  }
};

interface LocationSelectorProps {
  countryValue: string;
  stateValue: string;
  cityValue: string;
  onCountryChange: (value: string) => void;
  onStateChange: (value: string) => void;
  onCityChange: (value: string) => void;
  countryError?: string;
  stateError?: string;
  cityError?: string;
}

export function LocationSelector({
  countryValue,
  stateValue,
  cityValue,
  onCountryChange,
  onStateChange,
  onCityChange,
  countryError,
  stateError,
  cityError
}: LocationSelectorProps) {
  const [countryId, setCountryId] = useState<number>(0);
  const [stateId, setStateId] = useState<number>(0);
  const [showCityInput, setShowCityInput] = useState<boolean>(false);
  const [manualCity, setManualCity] = useState<string>('');
  const [selectedCountryName, setSelectedCountryName] = useState<string>('');
  const [selectedStateName, setSelectedStateName] = useState<string>('');

  // Filter cities from the fallback database based on country and state
  const filteredCities = useMemo(() => {
    if (!selectedCountryName || !selectedStateName) return [];

    // Use fallback cities database
    const fallbackCountryCities = fallbackCities[selectedCountryName as keyof typeof fallbackCities];
    if (fallbackCountryCities) {
      const stateCities = fallbackCountryCities[selectedStateName as keyof typeof fallbackCountryCities];
      if (stateCities) {
        return stateCities.map(cityName => ({
          name: cityName,
          country: selectedCountryName,
          state: selectedStateName,
          population: null
        }));
      }
    }

    return [];
  }, [selectedCountryName, selectedStateName]);

  // Initialize manual city input if city value exists but no state is selected
  useEffect(() => {
    if (cityValue && !stateId) {
      setManualCity(cityValue);
      setShowCityInput(true);
    }
  }, [cityValue, stateId]);

  const handleCountryChange = (e: any) => {
    setCountryId(e.id);
    setStateId(0); // Reset state when country changes
    setSelectedCountryName(e.name);
    setSelectedStateName('');
    onCountryChange(e.name);
    onStateChange(''); // Reset state
    onCityChange(''); // Reset city
    setShowCityInput(false);
    setManualCity('');
  };

  const handleStateChange = (e: any) => {
    setStateId(e.id);
    setSelectedStateName(e.name);
    onStateChange(e.name);
    onCityChange(''); // Reset city when state changes
    setShowCityInput(false);
    setManualCity('');
  };

  const handleCityChange = (e: any) => {
    onCityChange(e.name);
    setShowCityInput(false);
    setManualCity('');
  };

  const handleManualCityToggle = () => {
    setShowCityInput(!showCityInput);
    if (!showCityInput) {
      setManualCity(cityValue);
    } else {
      setManualCity('');
      onCityChange('');
    }
  };

  const handleManualCityChange = (value: string) => {
    setManualCity(value);
    onCityChange(value);
  };

  return (
    <>
      {/* Country Selector */}
      <FormItem>
        <FormLabel className="text-[#808080]">País</FormLabel>
        <FormControl>
          <div className="w-full">
            <CountrySelect
              onChange={handleCountryChange}
              placeHolder="Seleccionar País"
              containerClassName="w-full"
              inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
            />
          </div>
        </FormControl>
        {countryError && <FormMessage>{countryError}</FormMessage>}
      </FormItem>

      {/* State/Province Selector */}
      <FormItem>
        <FormLabel className="text-[#808080]">Provincia/Estado</FormLabel>
        <FormControl>
          <div className="w-full">
            <StateSelect
              countryid={countryId}
              onChange={handleStateChange}
              placeHolder="Seleccionar Provincia/Estado"
              containerClassName="w-full"
              inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
            />
          </div>
        </FormControl>
        {stateError && <FormMessage>{stateError}</FormMessage>}
      </FormItem>

      {/* City Selector or Manual Input */}
      <FormItem>
        <FormLabel className="text-[#808080]">Ciudad</FormLabel>
        <FormControl>
          <div className="w-full space-y-2">
            {showCityInput ? (
              <div className="space-y-2">
                <Input
                  placeholder="Ingrese la ciudad manualmente"
                  className="border-[#DCDBDB]"
                  value={manualCity}
                  onChange={(e) => handleManualCityChange(e.target.value)}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleManualCityToggle}
                  className="text-xs"
                >
                  Volver al selector de ciudades
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredCities.length > 0 ? (
                  <Select value={cityValue} onValueChange={(value) => onCityChange(value)}>
                    <SelectTrigger className="border-[#DCDBDB]">
                      <SelectValue placeholder="Seleccionar Ciudad" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      {filteredCities.map((city, index) => (
                        <SelectItem key={`${city.name}-${index}`} value={city.name}>
                          {city.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <CitySelect
                    countryid={countryId}
                    stateid={stateId}
                    onChange={handleCityChange}
                    placeHolder="Seleccionar Ciudad"
                    containerClassName="w-full"
                    inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
                  />
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleManualCityToggle}
                  className="text-xs text-blue-600 hover:text-blue-700"
                >
                  ¿No encuentras tu ciudad? Escribir manualmente
                </Button>
                {filteredCities.length > 0 && (
                  <p className="text-xs text-gray-500">
                    Mostrando {filteredCities.length} ciudades principales de {selectedStateName}.
                    {filteredCities.length >= 20 && " Usa el input manual para ciudades específicas."}
                  </p>
                )}
              </div>
            )}
          </div>
        </FormControl>
        {cityError && <FormMessage>{cityError}</FormMessage>}
      </FormItem>
    </>
  );
}
