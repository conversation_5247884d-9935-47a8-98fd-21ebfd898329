'use client';

import { useState, useEffect, useMemo } from 'react';
import {
  CountrySelect,
  StateSelect,
  CitySelect
} from 'react-country-state-city';
import 'react-country-state-city/dist/react-country-state-city.css';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';

// Import the comprehensive cities database
import cities from 'all-the-cities';

interface LocationSelectorProps {
  countryValue: string;
  stateValue: string;
  cityValue: string;
  onCountryChange: (value: string) => void;
  onStateChange: (value: string) => void;
  onCityChange: (value: string) => void;
  countryError?: string;
  stateError?: string;
  cityError?: string;
}

export function LocationSelector({
  countryValue,
  stateValue,
  cityValue,
  onCountryChange,
  onStateChange,
  onCityChange,
  countryError,
  stateError,
  cityError
}: LocationSelectorProps) {
  const [countryId, setCountryId] = useState<number>(0);
  const [stateId, setStateId] = useState<number>(0);
  const [showCityInput, setShowCityInput] = useState<boolean>(false);
  const [manualCity, setManualCity] = useState<string>('');
  const [selectedCountryName, setSelectedCountryName] = useState<string>('');
  const [selectedStateName, setSelectedStateName] = useState<string>('');

  // Filter cities from the comprehensive database based on country and state
  const filteredCities = useMemo(() => {
    if (!selectedCountryName || !selectedStateName) return [];

    // Filter cities by country and state/province
    const countryCities = cities.filter(city => {
      const countryMatch = city.country?.toLowerCase() === selectedCountryName.toLowerCase() ||
                          city.country?.toLowerCase().includes(selectedCountryName.toLowerCase());

      const stateMatch = city.adminCode?.toLowerCase() === selectedStateName.toLowerCase() ||
                        city.adminCode?.toLowerCase().includes(selectedStateName.toLowerCase()) ||
                        city.admin1?.toLowerCase() === selectedStateName.toLowerCase() ||
                        city.admin1?.toLowerCase().includes(selectedStateName.toLowerCase());

      return countryMatch && stateMatch;
    });

    // Sort by population (descending) and then by name
    return countryCities
      .sort((a, b) => {
        if (b.population !== a.population) {
          return (b.population || 0) - (a.population || 0);
        }
        return a.name.localeCompare(b.name);
      })
      .slice(0, 100); // Limit to top 100 cities for performance
  }, [selectedCountryName, selectedStateName]);

  // Initialize manual city input if city value exists but no state is selected
  useEffect(() => {
    if (cityValue && !stateId) {
      setManualCity(cityValue);
      setShowCityInput(true);
    }
  }, [cityValue, stateId]);

  const handleCountryChange = (e: any) => {
    setCountryId(e.id);
    setStateId(0); // Reset state when country changes
    setSelectedCountryName(e.name);
    setSelectedStateName('');
    onCountryChange(e.name);
    onStateChange(''); // Reset state
    onCityChange(''); // Reset city
    setShowCityInput(false);
    setManualCity('');
  };

  const handleStateChange = (e: any) => {
    setStateId(e.id);
    setSelectedStateName(e.name);
    onStateChange(e.name);
    onCityChange(''); // Reset city when state changes
    setShowCityInput(false);
    setManualCity('');
  };

  const handleCityChange = (e: any) => {
    onCityChange(e.name);
    setShowCityInput(false);
    setManualCity('');
  };

  const handleManualCityToggle = () => {
    setShowCityInput(!showCityInput);
    if (!showCityInput) {
      setManualCity(cityValue);
    } else {
      setManualCity('');
      onCityChange('');
    }
  };

  const handleManualCityChange = (value: string) => {
    setManualCity(value);
    onCityChange(value);
  };

  return (
    <>
      {/* Country Selector */}
      <FormItem>
        <FormLabel className="text-[#808080]">País</FormLabel>
        <FormControl>
          <div className="w-full">
            <CountrySelect
              onChange={handleCountryChange}
              placeHolder="Seleccionar País"
              containerClassName="w-full"
              inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
            />
          </div>
        </FormControl>
        {countryError && <FormMessage>{countryError}</FormMessage>}
      </FormItem>

      {/* State/Province Selector */}
      <FormItem>
        <FormLabel className="text-[#808080]">Provincia/Estado</FormLabel>
        <FormControl>
          <div className="w-full">
            <StateSelect
              countryid={countryId}
              onChange={handleStateChange}
              placeHolder="Seleccionar Provincia/Estado"
              containerClassName="w-full"
              inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
            />
          </div>
        </FormControl>
        {stateError && <FormMessage>{stateError}</FormMessage>}
      </FormItem>

      {/* City Selector or Manual Input */}
      <FormItem>
        <FormLabel className="text-[#808080]">Ciudad</FormLabel>
        <FormControl>
          <div className="w-full space-y-2">
            {showCityInput ? (
              <div className="space-y-2">
                <Input
                  placeholder="Ingrese la ciudad manualmente"
                  className="border-[#DCDBDB]"
                  value={manualCity}
                  onChange={(e) => handleManualCityChange(e.target.value)}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleManualCityToggle}
                  className="text-xs"
                >
                  Volver al selector de ciudades
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredCities.length > 0 ? (
                  <Select value={cityValue} onValueChange={(value) => onCityChange(value)}>
                    <SelectTrigger className="border-[#DCDBDB]">
                      <SelectValue placeholder="Seleccionar Ciudad" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      {filteredCities.map((city, index) => (
                        <SelectItem key={`${city.name}-${index}`} value={city.name}>
                          <div className="flex flex-col">
                            <span>{city.name}</span>
                            {city.population && (
                              <span className="text-xs text-gray-500">
                                Población: {city.population.toLocaleString()}
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <CitySelect
                    countryid={countryId}
                    stateid={stateId}
                    onChange={handleCityChange}
                    placeHolder="Seleccionar Ciudad"
                    containerClassName="w-full"
                    inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
                  />
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleManualCityToggle}
                  className="text-xs text-blue-600 hover:text-blue-700"
                >
                  ¿No encuentras tu ciudad? Escribir manualmente
                </Button>
                {filteredCities.length > 0 && (
                  <p className="text-xs text-gray-500">
                    Mostrando {filteredCities.length} ciudades principales.
                    {filteredCities.length === 100 && " Usa el input manual para ciudades específicas."}
                  </p>
                )}
              </div>
            )}
          </div>
        </FormControl>
        {cityError && <FormMessage>{cityError}</FormMessage>}
      </FormItem>
    </>
  );
}
