'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { getPatientDetails } from '../actions/getPatientDetails';
import { MedicalPatientDto } from '../types/patient';
import { toast } from 'sonner';

interface PatientDetailsModalProps {
  patientId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function PatientDetailsModal({
  patientId,
  isOpen,
  onClose
}: PatientDetailsModalProps) {
  const [patient, setPatient] = useState<MedicalPatientDto | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPatientDetails = async () => {
      try {
        setIsLoading(true);
        const data = await getPatientDetails(patientId);
        setPatient(data);
      } catch (error) {
        console.error('Error fetching patient details:', error);
        toast.error('Error al cargar los detalles del paciente');
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen && patientId) {
      fetchPatientDetails();
    }
  }, [isOpen, patientId]);

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Cargando detalles del paciente...</DialogTitle>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  if (!patient) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Detalles del Paciente</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center gap-4 py-4">
          <div className="relative h-32 w-32 overflow-hidden rounded-full">
            <Image
              src={patient.image}
              alt={`${patient.name} ${patient.last_name}`}
              fill
              className="object-cover"
            />
          </div>
          <div className="w-full space-y-2">
            <div className="flex justify-between border-b pb-2">
              <span className="font-semibold">Nombre:</span>
              <span>
                {patient.name} {patient.last_name}
              </span>
            </div>
            <div className="flex justify-between border-b pb-2">
              <span className="font-semibold">Email:</span>
              <span>{patient.email}</span>
            </div>
            <div className="flex justify-between border-b pb-2">
              <span className="font-semibold">Edad:</span>
              <span>{patient.age} años</span>
            </div>
            <div className="flex justify-between border-b pb-2">
              <span className="font-semibold">Diagnóstico CIE:</span>
              <span>{patient.main_diagnostic_cie || 'Sin diagnóstico'}</span>
            </div>
            {patient.notes && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-semibold">Notas:</span>
                <span>{patient.notes}</span>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
