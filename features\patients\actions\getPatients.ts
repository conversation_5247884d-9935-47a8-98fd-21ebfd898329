'use server';

import { get } from '../../../lib/api';
import { DashboardPatient } from '../types/dashboardTypes';

// Define the expected response structure from the API
export interface GetPatientsResponse {
  patients: DashboardPatient[];
  total: number;
}

/**
 * Fetches a list of patients with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of patients per page (default: 100 for combobox).
 * @returns A promise resolving to the list of patients and total count.
 */
// Set to true to use mock data, false to use real API
const USE_MOCK_DATA = false;

export async function getPatients(
  page: number = 1,
  pageSize: number = 100
): Promise<GetPatientsResponse> {
  try {
    let response: any;

    if (USE_MOCK_DATA) {
      // Mock data for development/testing
      console.log('Using mock data for patients');
      const mockPatients = [
        {
          id: '1',
          name: '<PERSON>',
          last_name: '<PERSON>',
          age: 35,
          main_diagnostic_cie:
            'Cólera debido a Vibrio cholerae 01, biotipo cholerae',
          email: '<EMAIL>',
          phone: '**********',
          prefix: '+54',
          identification_number: '12345678',
          identification_type: 'DNI',
          health_care_number: 'HC123456'
        },
        {
          id: '2',
          name: 'María',
          last_name: 'González',
          age: 28,
          main_diagnostic_cie: 'Hipertensión esencial',
          email: '<EMAIL>',
          phone: '**********',
          prefix: '+54',
          identification_number: '87654321',
          identification_type: 'DNI',
          health_care_number: 'HC654321'
        }
      ];
      response = mockPatients;
    } else {
      // Real API call
      const endpoint = `/patients`;
      console.log(`Fetching patients from: ${endpoint}`);
      response = await get<any>(endpoint);
    }

    console.log('Raw API response:', response);

    // Handle different response formats
    let patientsArray: any[] = [];
    let total = 0;

    if (Array.isArray(response)) {
      // If response is directly an array (mock data case)
      patientsArray = response;
      total = response.length;
      console.log(
        'Response is a direct array with',
        patientsArray.length,
        'patients'
      );
    } else if (response && Array.isArray(response.patients)) {
      // If response has a patients property
      patientsArray = response.patients;
      total = response.total || response.patients.length;
      console.log(
        'Response has patients property with',
        patientsArray.length,
        'patients'
      );
    } else if (response && Array.isArray(response.items)) {
      // If response uses 'items' property (common in paginated APIs)
      patientsArray = response.items;
      total =
        response.meta?.totalItems || response.total || response.items.length;
      console.log(
        'Response has items property with',
        patientsArray.length,
        'patients'
      );
    } else {
      console.warn('Received unexpected format for patients list:', response);
      return { patients: [], total: 0 };
    }

    // Transform the API response to match our DashboardPatient type
    const patients: DashboardPatient[] = patientsArray.map((p: any) => {
      return {
        id: String(p.id),
        name: p.user?.name || p.name || 'Nombre no disponible',
        last_name: p.user?.last_name || p.last_name || '',
        image: p.user?.image || p.image,
        age: p.user?.age || p.age || 0,
        main_diagnostic_cie:
          p.user?.main_diagnostic_cie ||
          p.main_diagnostic_cie ||
          'No especificado',
        email: p.user?.email || p.email || 'No disponible',
        phone: p.user?.phone || p.phone || 'No disponible',
        prefix: p.user?.prefix || p.prefix || '',
        identification_number:
          p.user?.identification_number || p.identification_number || '',
        identification_type:
          p.user?.identification_type || p.identification_type || '',
        health_care_number:
          p.patient?.health_care_number || p.health_care_number || ''
      };
    });

    console.log(`Successfully processed ${patients.length} patients.`);
    return {
      patients,
      total
    };
  } catch (error) {
    console.error('Error fetching patients:', error);
    if (error instanceof Error) {
      throw new Error(`Failed to fetch patients: ${error.message}`);
    }
    throw new Error('Failed to fetch patients due to an unexpected error.');
  }
}
