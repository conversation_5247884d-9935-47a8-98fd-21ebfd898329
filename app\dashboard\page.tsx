'use client';
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { getPatients } from '@/features/patients/actions/getPatients';
import { deletePatientTenant } from '@/features/patients/actions/deletePatientTenant';
import { ClinicalHistoryView } from '@/features/patients/components/ClinicalHistoryView';
import { PatientDetailsModal } from '@/features/patients/components/PatientDetailsModal';
import { EditPatientModal } from '@/features/patients/components/EditPatientModal';
import { SearchAndFilterBar } from '@/features/patients/components/SearchAndFilterBar';
import { PatientsTable } from '@/features/patients/components/PatientsTable';
import { DeletePatientDialog } from '@/features/patients/components/DeletePatientDialog';
import { PatientsPagination } from '@/features/patients/components/PatientsPagination';
import { usePatientFiltering } from '@/features/patients/hooks/usePatientFiltering';

import {
  DashboardPatient,
  SortCriteria,
  SortDirection
} from '@/features/patients/types/dashboardTypes';

const Page = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [isClinicalHistoryOpen, setIsClinicalHistoryOpen] = useState(false);
  const [sortBy, setSortBy] = useState<SortCriteria>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [patients, setPatients] = useState<DashboardPatient[]>([]);
  const [isPatientDetailsOpen, setIsPatientDetailsOpen] = useState(false);
  const [selectedPatientForDetails, setSelectedPatientForDetails] = useState<
    string | null
  >(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [patientToDelete, setPatientToDelete] =
    useState<DashboardPatient | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [patientToEditId, setPatientToEditId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [patientsPerPage] = useState(10);

  // State for server-side pagination
  const [totalPatients, setTotalPatients] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch patients data with pagination
  useEffect(() => {
    const fetchPatients = async () => {
      setIsLoading(true);
      try {
        const result = await getPatients(currentPage, patientsPerPage);
        setPatients(result.patients || []);
        setTotalPatients(result.total || 0);
        if (!result.patients || result.patients.length === 0) {
          toast.info('No hay pacientes que mostrar');
        }
      } catch (error) {
        console.error('Error fetching patients:', error);
        toast.error('Error al cargar los pacientes');
        setPatients([]);
        setTotalPatients(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPatients();
  }, [currentPage, patientsPerPage]);

  // Use the custom hook for filtering and sorting patients (client-side for search)
  const { filteredAndSortedPatients } = usePatientFiltering({
    patients,
    searchTerm,
    sortBy,
    sortDirection
  });

  // Calculate pagination based on whether we're searching or not
  const isSearching = searchTerm.trim().length > 0;
  const displayPatients = isSearching ? filteredAndSortedPatients : patients;
  const totalPages = isSearching
    ? Math.ceil(filteredAndSortedPatients.length / patientsPerPage)
    : Math.ceil(totalPatients / patientsPerPage);

  // For search, use client-side pagination; for normal view, use server data
  const paginatedPatients = isSearching
    ? filteredAndSortedPatients.slice(
        (currentPage - 1) * patientsPerPage,
        currentPage * patientsPerPage
      )
    : patients;

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // When search is cleared, refetch data for current page
  useEffect(() => {
    if (!searchTerm.trim()) {
      // Only refetch if we're not already on page 1 or if we have no data
      if (currentPage !== 1 || patients.length === 0) {
        const fetchPatients = async () => {
          setIsLoading(true);
          try {
            const result = await getPatients(currentPage, patientsPerPage);
            setPatients(result.patients || []);
            setTotalPatients(result.total || 0);
          } catch (error) {
            console.error('Error fetching patients:', error);
            toast.error('Error al cargar los pacientes');
          } finally {
            setIsLoading(false);
          }
        };
        fetchPatients();
      }
    }
  }, [searchTerm, currentPage, patientsPerPage, patients.length]);

  // Handler functions
  const handleViewClinicalHistory = (patientId: string) => {
    setSelectedPatientId(patientId);
    setIsClinicalHistoryOpen(true);
  };

  const handleViewPatientDetails = (patientId: string) => {
    setSelectedPatientForDetails(patientId);
    setIsPatientDetailsOpen(true);
  };

  const handleSortChange = (criteria: SortCriteria) => {
    if (sortBy === criteria) {
      // Toggle direction if clicking the same criteria
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new criteria and reset direction to ascending
      setSortBy(criteria);
      setSortDirection('asc');
    }
  };

  const handleEditClick = (patientId: string) => {
    setPatientToEditId(patientId);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (patient: DashboardPatient) => {
    setPatientToDelete(patient);
    setIsDeleteConfirmOpen(true);
  };

  const confirmDelete = useCallback(async () => {
    if (!patientToDelete) return;

    const patientId = patientToDelete.id;
    const patientName = patientToDelete.name;

    const promise = deletePatientTenant(patientId);

    toast.promise(promise, {
      loading: `Eliminando asociación de ${patientName}...`,
      success: (res) => {
        if (res.success) {
          // Optimistically remove patient from local state
          setPatients((prev) => prev.filter((p) => p.id !== patientId));
          setIsDeleteConfirmOpen(false);
          setPatientToDelete(null);
          return res.message || `${patientName} eliminado correctamente.`;
        } else {
          throw new Error(res.message || 'Error desconocido al eliminar.');
        }
      },
      error: (err) => {
        console.error('Deletion error:', err);
        setIsDeleteConfirmOpen(false);
        setPatientToDelete(null);
        return err.message || 'No se pudo eliminar la asociación del paciente.';
      }
    });
  }, [patientToDelete]);

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header Section with Search and Filters */}
      <div className="flex-shrink-0">
        <SearchAndFilterBar
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          sortBy={sortBy}
          onSortChange={handleSortChange}
          onAddPatient={() => router.push('/dashboard/new-patient')}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col bg-white overflow-hidden">
        {/* Table Section - scrollable */}
        <div className="flex-1 overflow-auto">
          <PatientsTable
            patients={paginatedPatients}
            onViewDetails={handleViewPatientDetails}
            onViewClinicalHistory={handleViewClinicalHistory}
            onEditPatient={handleEditClick}
            onDeletePatient={handleDeleteClick}
          />
        </div>

        {/* Pagination at bottom - only show if there are multiple pages */}
        {totalPages > 1 && (
          <div className="flex-shrink-0">
            <PatientsPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              totalItems={isSearching ? filteredAndSortedPatients.length : totalPatients}
              itemsPerPage={patientsPerPage}
            />
          </div>
        )}
      </div>

      {/* Clinical History Dialog */}
      {selectedPatientId && (
        <ClinicalHistoryView
          patientId={selectedPatientId}
          isOpen={isClinicalHistoryOpen}
          onClose={() => setIsClinicalHistoryOpen(false)}
        />
      )}

      {/* Patient Details Modal */}
      {selectedPatientForDetails && (
        <PatientDetailsModal
          patientId={selectedPatientForDetails}
          isOpen={isPatientDetailsOpen}
          onClose={() => {
            setIsPatientDetailsOpen(false);
            setSelectedPatientForDetails(null);
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeletePatientDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        patient={patientToDelete}
        onCancel={() => setPatientToDelete(null)}
        onConfirm={confirmDelete}
      />

      {/* Edit Patient Modal */}
      <EditPatientModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setPatientToEditId(null);
        }}
        patientId={patientToEditId}
      />
    </div>
  );
};

export default Page;
