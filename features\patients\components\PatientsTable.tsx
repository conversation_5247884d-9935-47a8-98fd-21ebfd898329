'use client';

import Image from 'next/image';
import Avatar from '@/features/patients/assets/AvarDefault.png';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { PatientActions } from './PatientActions';
import { PatientTableProps } from '../types/dashboardTypes';
import { useRouter } from 'next/navigation';

export function PatientsTable({
  patients,
  onViewDetails,
  onViewClinicalHistory,
  onEditPatient,
  onDeletePatient
}: PatientTableProps) {
  const router = useRouter();

  const handlePatientClick = (patientId: string) => {
    router.push(`/dashboard/pacientes/${patientId}/perfil`);
  };

  return (
    <div className="w-full h-full">
      <Table className="w-full">
        <TableHeader className="sticky top-0 bg-white z-10">
          <TableRow className="border-b border-gray-200 bg-gray-50">
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Nombre
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Edad
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Diagnóstico
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Email
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Teléfono
            </TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {patients.map((patient) => (
            <TableRow
              key={patient.id}
              className="border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer"
              onClick={() => handlePatientClick(patient.id)}
            >
              <TableCell className="py-4 px-6">
                <div className="flex items-center space-x-3">
                  <Image
                    src={Avatar}
                    alt={`Foto de ${patient.name}`}
                    className="h-10 w-10 rounded-lg object-cover"
                  />
                  <span className="font-medium text-gray-900 hover:text-blue-600 transition-colors">
                    {patient.name} {patient.last_name}
                  </span>
                </div>
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.age ? `${patient.age} años` : 'N/A'}
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.main_diagnostic_cie || 'Sin diagnóstico'}
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.email}
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.phone}
              </TableCell>
              <TableCell
                className="py-4 px-6"
                onClick={(e) => e.stopPropagation()}
              >
                <PatientActions
                  patient={patient}
                  onViewDetails={onViewDetails}
                  onViewClinicalHistory={onViewClinicalHistory}
                  onEditPatient={onEditPatient}
                  onDeletePatient={onDeletePatient}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
