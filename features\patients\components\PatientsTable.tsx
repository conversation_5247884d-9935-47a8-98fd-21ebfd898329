'use client';

import Image from 'next/image';
import Avatar from '@/features/patients/assets/AvarDefault.png';
// Removed Table components import - using native HTML table for better layout control
import { PatientActions } from './PatientActions';
import { PatientTableProps } from '../types/dashboardTypes';
import { useRouter } from 'next/navigation';

export function PatientsTable({
  patients,
  onViewDetails,
  onViewClinicalHistory,
  onEditPatient,
  onDeletePatient
}: PatientTableProps) {
  const router = useRouter();

  const handlePatientClick = (patientId: string) => {
    router.push(`/dashboard/pacientes/${patientId}/perfil`);
  };

  return (
    <div className="w-full">
      <div className="w-full">
        <table className="w-full caption-bottom text-sm">
          <thead className="[&_tr]:border-b">
            <tr className="border-b border-gray-200 bg-gray-50">
              <th className="text-left font-medium text-gray-600 py-3 px-6 h-10 align-middle">
                Nombre
              </th>
              <th className="text-left font-medium text-gray-600 py-3 px-6 h-10 align-middle">
                Edad
              </th>
              <th className="text-left font-medium text-gray-600 py-3 px-6 h-10 align-middle">
                Diagnóstico
              </th>
              <th className="text-left font-medium text-gray-600 py-3 px-6 h-10 align-middle">
                Email
              </th>
              <th className="text-left font-medium text-gray-600 py-3 px-6 h-10 align-middle">
                Teléfono
              </th>
              <th className="w-12 h-10 align-middle"></th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {patients.map((patient) => (
              <tr
                key={patient.id}
                className="border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => handlePatientClick(patient.id)}
              >
                <td className="py-4 px-6 p-2 align-middle">
                  <div className="flex items-center space-x-3">
                    <Image
                      src={Avatar}
                      alt={`Foto de ${patient.name}`}
                      className="h-10 w-10 rounded-lg object-cover"
                    />
                    <span className="font-medium text-gray-900 hover:text-blue-600 transition-colors">
                      {patient.name} {patient.last_name}
                    </span>
                  </div>
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.age ? `${patient.age} años` : 'N/A'}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.main_diagnostic_cie || 'Sin diagnóstico'}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.email}
                </td>
                <td className="py-4 px-6 text-gray-600 p-2 align-middle">
                  {patient.phone}
                </td>
                <td
                  className="py-4 px-6 p-2 align-middle"
                  onClick={(e) => e.stopPropagation()}
                >
                  <PatientActions
                    patient={patient}
                    onViewDetails={onViewDetails}
                    onViewClinicalHistory={onViewClinicalHistory}
                    onEditPatient={onEditPatient}
                    onDeletePatient={onDeletePatient}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
