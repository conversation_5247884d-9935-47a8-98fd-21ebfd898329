'use client';

import { useEffect, useState, useRef } from 'react'; // Added useState, useRef
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { But<PERSON> } from '../../../components/ui/button';
import { ArrowLeft, Upload, Save } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '../../../components/ui/form';
import { Input } from '../../../components/ui/input';
import { Card, CardContent } from '../../../components/ui/card';
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from '../../../components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../../../components/ui/select';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import {
  CountrySelect,
  StateSelect,
  CitySelect
} from 'react-country-state-city';
import 'react-country-state-city/dist/react-country-state-city.css';

import { createPatient, CreatePatientPayload } from '../actions/createPatient'; // Import CreatePatientPayload type
import { updatePatient } from '../actions/updatePatient'; // Added update action
import { MedicalPatientDto, UpdatePatientPayloadDto } from '../types/patient'; // Added types
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

const formSchema = z.object({
  photo: z.string().optional(),
  name: z.string().min(2, {
    message: 'El nombre debe tener al menos 2 caracteres.'
  }),
  lastName: z.string().min(2, {
    message: 'El apellido debe tener al menos 2 caracteres.'
  }),
  email: z.string().email({
    message: 'Por favor ingrese un email válido.'
  }),
  identification_type: z.string().min(1, {
    message: 'El tipo de identificación es requerido.'
  }),
  identification_number: z
    .string()
    .min(7, {
      message: 'El número de identificación debe tener al menos 7 caracteres.'
    })
    .max(15),
  birth_date: z.string().min(1, {
    message: 'La fecha de nacimiento es requerida.'
  }),
  nationality: z
    .string()
    .min(1, {
      message: 'La nacionalidad es requerida.'
    })
    .default('Argentina'),
  gender: z.enum(['Masculino', 'Femenino', 'Otro'], {
    required_error: 'El género es requerido.'
  }),
  phonePrefix: z
    .string()
    .min(1, {
      message: 'El prefijo telefónico es requerido.'
    })
    .default('54'),
  phone: z.string().min(10, {
    message: 'El teléfono debe tener al menos 10 dígitos.'
  }),
  direction: z.string().min(1, {
    message: 'La dirección es requerida.'
  }),
  country: z
    .string()
    .min(1, {
      message: 'El país es requerido.'
    })
    .default('Argentina'),
  province: z.string().min(1, {
    message: 'La provincia es requerida.'
  }),
  city: z.string().min(1, {
    message: 'La ciudad es requerida.'
  }),
  postalCode: z.string().min(1, {
    message: 'El código postal es requerido.'
  }),
  directionNumber: z.string().min(1, {
    message: 'El número de dirección es requerido.'
  }),
  apartment: z.string().optional(),
  healthCareNumber: z.string().optional()
});

interface PatientCreateProps {
  isEditMode?: boolean;
  patientId?: string;
  initialData?: MedicalPatientDto | null; // Allow null or undefined
  onClose?: () => void; // Optional callback for closing modal/view
}

const PatientCreate: React.FC<PatientCreateProps> = ({
  isEditMode = false,
  patientId,
  initialData,
  onClose
}) => {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [countryId, setCountryId] = useState<number>(0);
  const [stateId, setStateId] = useState<number>(0);

  // Function to format ISO date string to YYYY-MM-DD for input type="date"
  const formatDateForInput = (isoDate?: string | null) => {
    if (!isoDate) return '';
    try {
      return isoDate.split('T')[0];
    } catch (e) {
      console.error('Error formatting date:', isoDate, e);
      return '';
    }
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues:
      isEditMode && initialData
        ? {
            name: initialData.user.name || '',
            lastName: initialData.user.last_name || '',
            email: initialData.user.email || '',
            identification_type: initialData.user.identification_type || 'Dni',
            identification_number: initialData.user.identification_number || '',
            birth_date: formatDateForInput(initialData.user.birth_date),
            nationality: initialData.user.nationality || 'Argentina',
            gender: (initialData.user.gender as 'Masculino' | 'Femenino' | 'Otro') || 'Masculino',
            phonePrefix: initialData.user.phone_prefix || '54',
            phone: initialData.user.phone || '',
            photo: initialData.user.image || '', // Keep initial photo URL from data
            direction: initialData.patient.direction || '',
            country: initialData.patient.country || 'Argentina',
            province: initialData.patient.province || '',
            city: initialData.patient.city || '',
            postalCode: initialData.patient.postal_code || '',
            directionNumber: initialData.patient.direction_number || '',
            apartment: initialData.patient.apartment || '',
            healthCareNumber: initialData.patient.health_care_number || ''
          }
        : {
            // Default values for create mode
            country: 'Argentina',
            nationality: 'Argentina',
            phonePrefix: '54',
            name: '',
            lastName: '',
            email: '',
            identification_type: 'Dni',
            identification_number: '',
            birth_date: '',
            gender: 'Masculino' as const,
            phone: '',
            photo: '',
            direction: '',
            province: '',
            city: '',
            postalCode: '',
            directionNumber: '',
            apartment: '',
            healthCareNumber: ''
          }
  });

  // Reset form if initialData changes (useful if used in a modal that re-renders)
  useEffect(() => {
    if (isEditMode && initialData) {
      form.reset({
        name: initialData.user.name || '',
        lastName: initialData.user.last_name || '',
        email: initialData.user.email || '',
        identification_type: initialData.user.identification_type || 'Dni',
        identification_number: initialData.user.identification_number || '',
        birth_date: formatDateForInput(initialData.user.birth_date),
        nationality: initialData.user.nationality || 'Argentina',
        gender: (initialData.user.gender as 'Masculino' | 'Femenino' | 'Otro') || 'Masculino',
        phonePrefix: initialData.user.phone_prefix || '54',
        phone: initialData.user.phone || '',
        photo: initialData.user.image || '', // Keep initial photo URL from data
        direction: initialData.patient.direction || '',
        country: initialData.patient.country || 'Argentina',
        province: initialData.patient.province || '',
        city: initialData.patient.city || '',
        postalCode: initialData.patient.postal_code || '',
        directionNumber: initialData.patient.direction_number || '',
        apartment: initialData.patient.apartment || '',
        healthCareNumber: initialData.patient.health_care_number || ''
      });
    } else if (!isEditMode) {
      form.reset({
        // Reset to create defaults if switching mode
        country: 'Argentina',
        nationality: 'Argentina',
        phonePrefix: '54',
        name: '',
        lastName: '',
        email: '',
        identification_type: 'Dni',
        identification_number: '',
        birth_date: '',
        gender: 'Masculino' as const,
        phone: '',
        photo: '', // Keep photo field for potential URL
        direction: '',
        province: '',
        city: '',
        postalCode: '',
        directionNumber: '',
        apartment: '',
        healthCareNumber: ''
      });
    }
  }, [initialData, isEditMode, form]); // Use form directly instead of form.reset

  // Effect to set initial preview if editing with an existing image URL
  useEffect(() => {
    if (isEditMode && initialData?.user.image) {
      setPreviewUrl(initialData.user.image);
      setSelectedFile(null); // Ensure no file is selected initially in edit mode
    } else if (!isEditMode) {
      setPreviewUrl(null); // Clear preview in create mode
      setSelectedFile(null);
    }
  }, [isEditMode, initialData]);

  // Effect to revoke object URL on unmount or when file changes
  useEffect(() => {
    let objectUrl: string | null = null;
    if (selectedFile) {
      objectUrl = URL.createObjectURL(selectedFile);
      setPreviewUrl(objectUrl);
    }

    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
        setPreviewUrl(null); // Clear preview when file changes or unmounts
      }
    };
  }, [selectedFile]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      // For now, we'll just show a preview but won't upload the file
      // In a real implementation, you would upload the file to a service like Cloudinary
      // and then set the returned URL to the form
      toast.info(
        'Imagen seleccionada. Nota: La subida de imágenes será implementada próximamente.'
      );
    } else {
      setSelectedFile(null);
      toast.error('Por favor seleccione un archivo de imagen válido.');
    }
    // Reset file input value to allow selecting the same file again
    if (event.target) {
      event.target.value = '';
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleDeletePhoto = () => {
    setSelectedFile(null);
    setPreviewUrl(isEditMode ? initialData?.user.image || null : null); // Revert to initial image or null
    form.setValue('photo', isEditMode ? initialData?.user.image || '' : ''); // Reset form field value
    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Clear the file input
    }
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    let promise: Promise<any>; // Promise type will be determined below

    if (isEditMode) {
      // --- EDIT MODE ---
      const updatePayload: UpdatePatientPayloadDto = { user: {}, patient: {} };

      // Populate user fields that changed
      if (values.name !== initialData?.user.name)
        updatePayload.user!.name = values.name;
      if (values.lastName !== initialData?.user.last_name)
        updatePayload.user!.last_name = values.lastName;
      if (values.email !== initialData?.user.email)
        updatePayload.user!.email = values.email;
      if (values.identification_type !== initialData?.user.identification_type)
        updatePayload.user!.identification_type = values.identification_type;
      if (
        values.identification_number !== initialData?.user.identification_number
      )
        updatePayload.user!.identification_number =
          values.identification_number;
      if (values.birth_date !== formatDateForInput(initialData?.user.birth_date))
        updatePayload.user!.birth_date = values.birth_date;
      if (values.nationality !== initialData?.user.nationality)
        updatePayload.user!.nationality = values.nationality;
      if (values.gender !== initialData?.user.gender)
        updatePayload.user!.gender = values.gender;
      if (values.phonePrefix !== initialData?.user.phone_prefix)
        updatePayload.user!.phone_prefix = values.phonePrefix;
      if (values.phone !== initialData?.user.phone)
        updatePayload.user!.phone = values.phone;
      if ((values.photo || null) !== (initialData?.user.image || null))
        // IMPORTANT: Sending the actual file is not handled here.
        // The 'image' field in the payload still uses values.photo (expected URL).
        // A separate upload mechanism is required to handle 'selectedFile'.
        updatePayload.user!.image = values.photo || null; // Send updated URL or null

      // Populate patient fields that changed
      if (values.direction !== initialData?.patient.direction)
        updatePayload.patient!.direction = values.direction;
      if (values.country !== initialData?.patient.country)
        updatePayload.patient!.country = values.country;
      if (values.province !== initialData?.patient.province)
        updatePayload.patient!.province = values.province;
      if (values.city !== initialData?.patient.city)
        updatePayload.patient!.city = values.city;
      if (values.postalCode !== initialData?.patient.postal_code)
        updatePayload.patient!.postal_code = values.postalCode;
      // Use undefined for empty optional fields in PATCH payload
      if (
        values.directionNumber !== (initialData?.patient.direction_number ?? '')
      ) {
        // Compare with empty string if initial is null/undefined
        updatePayload.patient!.direction_number =
          values.directionNumber || undefined; // Send undefined if empty
      }
      if (
        (values.apartment || undefined) !==
        (initialData?.patient.apartment || undefined)
      ) {
        // Compare with undefined
        updatePayload.patient!.apartment = values.apartment || undefined; // Send undefined if empty
      }
      if (
        (values.healthCareNumber || undefined) !==
        (initialData?.patient.health_care_number || undefined)
      ) {
        // Compare with undefined
        updatePayload.patient!.health_care_number =
          values.healthCareNumber || undefined; // Send undefined if empty
      }

      // Clean empty objects
      if (Object.keys(updatePayload.user!).length === 0)
        delete updatePayload.user;
      if (Object.keys(updatePayload.patient!).length === 0)
        delete updatePayload.patient;

      // If no changes, inform user and return
      if (!updatePayload.user && !updatePayload.patient) {
        toast.info('No se detectaron cambios.');
        if (onClose) onClose();
        return;
      }

      promise = updatePatient(patientId!, updatePayload);
    } else {
      // --- CREATE MODE ---
      const createPayload: CreatePatientPayload = {
        user: {
          name: values.name,
          last_name: values.lastName,
          email: values.email,
          identification_type: values.identification_type,
          identification_number: values.identification_number,
          birth_date: values.birth_date,
          nationality: values.nationality,
          gender: values.gender,
          phone_prefix: values.phonePrefix,
          phone: values.phone,
          // Only include image if it's a valid URL
          ...(values.photo && values.photo.trim() !== ''
            ? { image: values.photo }
            : {})
        },
        patient: {
          direction: values.direction,
          country: values.country,
          province: values.province,
          city: values.city,
          postal_code: values.postalCode,
          direction_number: values.directionNumber || null,
          apartment: values.apartment || null,
          health_care_number: values.healthCareNumber || null
        }
      };

      promise = createPatient(createPayload);
    }

    // --- Execute Promise with Toast ---
    // Note: 'promise' was already declared at the start of the function
    toast.promise(promise, {
      loading: isEditMode ? 'Actualizando paciente...' : 'Creando paciente...',
      success: (result) => {
        // Handle different success responses based on the mode
        let successMessage = '';
        if (isEditMode) {
          // updatePatient returns { success: boolean, message?: string, data?: MedicalPatientDto }
          const updateResult = result as {
            success: boolean;
            message?: string;
            data?: MedicalPatientDto;
          };
          if (updateResult.success) {
            successMessage =
              updateResult.message || 'Paciente actualizado exitosamente';
          } else {
            // Throw error to trigger toast's error state
            throw new Error(updateResult.message || 'Error al actualizar.');
          }
        } else {
          // createPatient returns MedicalPatientDto on success (or throws on error)
          const createResult = result as MedicalPatientDto;
          // Basic check if the result looks like a patient object
          if (createResult && createResult.user && createResult.patient) {
            successMessage = 'Paciente creado exitosamente';
          } else {
            // If API returns something else on success, adjust this check
            console.warn(
              'Create patient success response format unexpected:',
              result
            );
            // Assume success if no error was thrown by createPatient action
            successMessage = 'Paciente creado (respuesta inesperada)';
            // Or throw an error if a specific structure is absolutely required
            // throw new Error('Respuesta inesperada al crear paciente.');
          }
        }

        if (onClose) onClose(); // Close modal on success
        else router.push('/dashboard'); // Redirect if not in modal context
        return successMessage; // Return the message for the toast
      },
      error: (err) => {
        // Error is already an instance of Error (thrown by actions or the success handler)
        console.error(
          isEditMode
            ? 'Error al actualizar paciente:'
            : 'Error al crear paciente:',
          err
        );

        // Check for specific error types
        if (
          err.message &&
          err.message.includes(
            'Unique constraint failed on the fields: (`identification_number`)'
          )
        ) {
          return 'Ya existe un paciente con este número de identificación. Por favor, verifique el número ingresado.';
        }
        if (
          err.message &&
          err.message.includes(
            'Unique constraint failed on the fields: (`email`)'
          )
        ) {
          return 'Ya existe un paciente con este email. Por favor, use un email diferente.';
        }

        return (
          err.message ||
          (isEditMode
            ? 'Error al actualizar el paciente'
            : 'Error al crear el paciente')
        );
      }
    });
  }

  return (
    <div className="flex-1 p-6 max-h-screen overflow-hidden">
      <div className="mb-4 flex items-center gap-2">
        {/* Show back button only if not in edit mode (assuming edit is in a modal) */}
        {!isEditMode && (
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Regresar
          </Button>
        )}
        {/* Conditionally render title */}
        <h1 className="text-2xl font-semibold"></h1>
      </div>

      <Card className="mb-4 h-[calc(100vh-140px)] overflow-y-auto">
        <CardContent className="p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex gap-6">
              {/* Photo Upload */}
              <div className="flex w-48 flex-col items-center gap-3">
                {/* Hidden File Input */}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileSelect}
                  accept="image/*" // Only accept image files
                  style={{ display: 'none' }} // Hide the default input
                />
                <Avatar
                  className="h-32 w-32 cursor-pointer"
                  onClick={handleUploadClick}
                >
                  <AvatarImage src={previewUrl ?? undefined} />{' '}
                  {/* Use previewUrl state */}
                  <AvatarFallback>PA</AvatarFallback>
                </Avatar>
                <div className="flex w-full flex-col gap-2">
                  <Button
                    variant="outline"
                    type="button"
                    className="flex-1 bg-[#487FFA] text-white hover:bg-[#487FFA]/90 text-sm border-[#487FFA] px-4 py-2"
                    onClick={handleUploadClick} // Trigger file input click
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Subir foto
                  </Button>
                  <Button
                    variant="ghost"
                    type="button"
                    className="flex-1 h-11 justify-center !opacity-100 font-Poppins text-red-600 hover:bg-red-50 hover:text-red-700 text-sm px-4 py-2"
                    onClick={handleDeletePhoto} // Clear selection
                    disabled={!previewUrl && !selectedFile} // Disable if no photo/preview
                  >
                    Eliminar foto
                  </Button>
                </div>
              </div>

              {/* Form Fields */}
              <div className="flex-1 space-y-4">
                {/* Personal Information */}
                <div>
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Nombre(s)
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el nombre"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Apellido(s)
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el apellido"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="birth_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Fecha de Nacimiento
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Género
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="border-[#DCDBDB]">
                                <SelectValue placeholder="Seleccione el género" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Masculino">Masculino</SelectItem>
                              <SelectItem value="Femenino">Femenino</SelectItem>
                              <SelectItem value="Otro">Otro</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Correo electrónico
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Ingrese el email"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Celular
                          </FormLabel>
                          <FormControl>
                            <PhoneInput
                              country={'ar'}
                              value={field.value}
                              onChange={(phone, countryData: any) => {
                                field.onChange(phone);
                                // Extract country code and set phonePrefix
                                if (countryData && countryData.dialCode) {
                                  form.setValue(
                                    'phonePrefix',
                                    countryData.dialCode
                                  );
                                }
                              }}
                              inputStyle={{
                                width: '100%',
                                height: '40px',
                                border: '1px solid #DCDBDB',
                                borderRadius: '6px',
                                fontSize: '14px',
                                paddingLeft: '48px'
                              }}
                              buttonStyle={{
                                border: '1px solid #DCDBDB',
                                borderRadius: '6px 0 0 6px',
                                backgroundColor: 'white'
                              }}
                              dropdownStyle={{
                                borderRadius: '6px',
                                border: '1px solid #DCDBDB'
                              }}
                              containerStyle={{
                                width: '100%'
                              }}
                              placeholder="Ingrese el número"
                              enableSearch={true}
                              searchPlaceholder="Buscar país..."
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Identification fields in one row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="identification_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[#808080]">
                              Tipo de Identificación
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="border-[#DCDBDB]">
                                  <SelectValue placeholder="Seleccione el tipo" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="Dni">DNI</SelectItem>
                                <SelectItem value="Pasaporte">
                                  Pasaporte
                                </SelectItem>
                                <SelectItem value="Cedula">Cédula</SelectItem>
                                <SelectItem value="Libreta_civica">
                                  Libreta Cívica
                                </SelectItem>
                                <SelectItem value="Libreta_enrolamiento">
                                  Libreta de Enrolamiento
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="identification_number"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[#808080]">
                              Número de Identificación
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Ingrese el número"
                                className="border-[#DCDBDB]"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="healthCareNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Número de Obra Social
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el número"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Address Information */}
                <div>
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="direction"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Dirección
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese la dirección"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">País</FormLabel>
                          <FormControl>
                            <div className="w-full">
                              <CountrySelect
                                onChange={(e: any) => {
                                  setCountryId(e.id);
                                  setStateId(0); // Reset state when country changes
                                  field.onChange(e.name);
                                  form.setValue('province', ''); // Reset province
                                  form.setValue('city', ''); // Reset city
                                }}
                                placeHolder="Seleccionar País"
                                containerClassName="w-full"
                                inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="province"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Provincia/Estado
                          </FormLabel>
                          <FormControl>
                            <div className="w-full">
                              <StateSelect
                                countryid={countryId}
                                onChange={(e: any) => {
                                  setStateId(e.id);
                                  field.onChange(e.name);
                                  form.setValue('city', ''); // Reset city when state changes
                                }}
                                placeHolder="Seleccionar Provincia/Estado"
                                containerClassName="w-full"
                                inputClassName="w-full h-10 px-3 border border-[#DCDBDB] rounded-md bg-white"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Ciudad
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese la ciudad"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="postalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Código Postal
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el código postal"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="directionNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Número
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el número"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="apartment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Piso/Departamento
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese piso/depto"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex justify-end pt-2">
                  <Button
                    type="submit"
                    className="bg-[#487FFA] hover:bg-[#487FFA]/90 px-6 py-2"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isEditMode ? 'Actualizar Paciente' : 'Guardar Paciente'}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default PatientCreate;
